// 文件路径: lib/src/core/networking/api_client.dart

import 'package:dio/dio.dart';

class ApiClient {
  late final Dio _dio;

  // 统一的后端基础地址
  static const String baseUrl = "http://10.0.2.2:8000";

  // 默认构造函数：内部创建 Dio 实例
  ApiClient() {
    _dio = Dio();
    _configureDio();
  }

  // 新增：允许注入已经配置好的 Dio 实例
  ApiClient.fromDio(Dio dio) : _dio = dio {
    _configureDio();
  }

  // 统一配置 Dio
  void _configureDio() {
    _dio.options
      ..baseUrl = baseUrl
      ..connectTimeout = const Duration(seconds: 10)
      ..receiveTimeout = const Duration(seconds: 10)
      ..headers = {
        'Content-Type': 'application/json',
      };

    // 日志拦截器保持与之前一致
    _dio.interceptors.add(LogInterceptor(
      requestBody: true,
      responseBody: true,
      logPrint: (obj) => print(obj.toString()),
    ));
  }

  Dio get dio => _dio;
}
